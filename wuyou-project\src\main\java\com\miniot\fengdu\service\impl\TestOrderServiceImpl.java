package com.miniot.fengdu.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miniot.fengdu.entity.CartOrder;
import com.miniot.fengdu.entity.OrderInfo;
import com.miniot.fengdu.entity.dto.OrderListDTO;
import com.miniot.fengdu.repository.TestOrderRepository;
import com.miniot.fengdu.service.TestOrderService;
import com.miniot.fengdu.util.OrderNoUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * 测试订单服务实现类
 */
@Service
public class TestOrderServiceImpl implements TestOrderService {

    private static final Logger logger = LoggerFactory.getLogger(TestOrderServiceImpl.class);

    @Autowired
    private TestOrderRepository testOrderRepository;

    @Override
    public IPage<OrderListDTO> getTestOrderList(Integer page, Integer size, String status, String type, String startTime, String endTime) {
        logger.info("=== 开始查询测试订单列表 ===");
        logger.info("查询参数: page={}, size={}, status={}, type={}, startTime={}, endTime={}",
                   page, size, status, type, startTime, endTime);

        // 如果不传分页参数，设置为查询所有数据
        if (page == null || size == null) {
            page = 1;
            size = Integer.MAX_VALUE;
            logger.info("未传入分页参数，将查询所有数据");
        }

        List<OrderListDTO> allOrders = new ArrayList<>();

        // 1. 查询测试单一商品订单
        if (type == null || (!"购物车订单".equals(type) && !"有礼".equals(type))) {
            logger.info("查询测试单一商品订单...");
            Page<OrderListDTO> singleOrderPageParam = new Page<>(1, Integer.MAX_VALUE);
            IPage<OrderListDTO> singleOrderResult = testOrderRepository.selectTestOrderListPage(singleOrderPageParam, status, type, startTime, endTime);
            allOrders.addAll(singleOrderResult.getRecords());
            logger.info("测试单一商品订单查询结果: {}", singleOrderResult.getRecords().size());
        }

        // 2. 查询测试购物车订单
        if (type == null || "购物车订单".equals(type) || "有礼".equals(type)) {
            logger.info("查询测试购物车订单...");
            Page<OrderListDTO> cartOrderPageParam = new Page<>(1, Integer.MAX_VALUE);
            IPage<OrderListDTO> cartOrderResult = testOrderRepository.selectTestCartOrderListPage(cartOrderPageParam, status, type, startTime, endTime);
            allOrders.addAll(cartOrderResult.getRecords());
            logger.info("测试购物车订单查询结果: {}", cartOrderResult.getRecords().size());
        }

        // 3. 按创建时间倒序排序
        allOrders.sort((o1, o2) -> {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                Date date1 = sdf.parse(o1.getCreateTime());
                Date date2 = sdf.parse(o2.getCreateTime());
                return date2.compareTo(date1); // 倒序
            } catch (Exception e) {
                logger.warn("时间解析失败: {}, {}", o1.getCreateTime(), o2.getCreateTime());
                return 0;
            }
        });

        long totalCount = allOrders.size();

        // 4. 手动分页
        List<OrderListDTO> pagedOrders;
        if (page != null && size != null && size != Integer.MAX_VALUE) {
            int start = (page - 1) * size;
            int end = Math.min(start + size, allOrders.size());
            pagedOrders = start < allOrders.size() ? allOrders.subList(start, end) : new ArrayList<>();
        } else {
            pagedOrders = allOrders;
        }

        // 5. 构造分页结果
        Page<OrderListDTO> result = new Page<>(page, size);
        result.setRecords(pagedOrders);
        result.setTotal(totalCount);
        result.setPages((totalCount + size - 1) / size);

        logger.info("测试订单列表查询完成: total={}, records={}", result.getTotal(), result.getRecords().size());
        logger.info("=== 测试订单列表查询完成 ===");

        return result;
    }

    @Override
    @Transactional
    public String generateTestOrderData(Integer orderCount, Integer cartOrderCount, Integer orderType) {
        // 设置默认值
        orderCount = orderCount != null ? orderCount : 0;
        cartOrderCount = cartOrderCount != null ? cartOrderCount : 0;

        // 验证订单类型
        if (orderType != null && (orderType < 1 || orderType > 3)) {
            throw new IllegalArgumentException("订单类型必须是1(五有)、2(有礼)或3(新研)");
        }

        logger.info("开始生成测试数据: orderCount={}, cartOrderCount={}, orderType={}", orderCount, cartOrderCount, orderType);

        try {
            Random random = new Random();

            int orderResult = 0;
            int cartOrderResult = 0;

            // 处理订单生成（使用现有测试用户）
            if (orderCount > 0 || cartOrderCount > 0) {
                List<Long> testUserIds = testOrderRepository.selectExistingTestUserIds();
                if (testUserIds.isEmpty()) {
                    throw new RuntimeException("没有可用的测试用户，请先确保wy_user_test表中有数据");
                }

                // 生成测试单一商品订单
                if (orderCount > 0) {
                    List<OrderInfo> testOrders = generateTestSingleOrders(orderCount, testUserIds, random, orderType);
                    orderResult = testOrderRepository.batchInsertTestOrders(testOrders);
                    logger.info("生成测试单一商品订单: {}", orderResult);
                }

                // 生成测试购物车订单
                if (cartOrderCount > 0) {
                    List<CartOrder> testCartOrders = generateTestCartOrders(cartOrderCount, testUserIds, random, orderType);
                    cartOrderResult = testOrderRepository.batchInsertTestCartOrders(testCartOrders);
                    logger.info("生成测试购物车订单: {}", cartOrderResult);
                }
            }

            String result = String.format("成功生成测试数据 - 单一商品订单: %d, 购物车订单: %d",
                                        orderResult, cartOrderResult);
            logger.info(result);

            // 添加详细信息
            if (orderCount == 0 && cartOrderCount == 0) {
                return "未指定任何生成参数，没有生成数据";
            }

            return result;

        } catch (Exception e) {
            logger.error("生成测试数据失败", e);
            throw new RuntimeException("生成测试数据失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public String clearTestOrderData() {
        logger.info("开始清空测试数据");

        try {
            // 清空测试表数据
            int orderResult = testOrderRepository.clearTestOrders();
            int cartOrderResult = testOrderRepository.clearTestCartOrders();
            int userResult = testOrderRepository.clearTestUsers();

            String result = String.format("成功清空测试数据 - 单一商品订单: %d, 购物车订单: %d, 用户: %d",
                                        orderResult, cartOrderResult, userResult);
            logger.info(result);
            return result;

        } catch (Exception e) {
            logger.error("清空测试数据失败", e);
            throw new RuntimeException("清空测试数据失败: " + e.getMessage());
        }
    }



    /**
     * 生成测试单一商品订单数据
     */
    private List<OrderInfo> generateTestSingleOrders(Integer orderCount, List<Long> userIds, Random random, Integer orderType) {
        List<OrderInfo> orders = new ArrayList<>();
        String[] titles = {"有机蔬菜田地认养", "优质水果采摘", "生态农场体验", "绿色蔬菜配送", "农产品礼盒"};
        String[] statuses = {"未支付", "已支付", "处理中", "已发货", "已完成", "已取消"};
        Integer[] types = {1, 2, 3}; // 1:五有，2:有礼，3:新研

        for (int i = 1; i <= orderCount; i++) {
            OrderInfo order = new OrderInfo();
            order.setTitle(titles[random.nextInt(titles.length)]);
            // 使用真实的订单号生成规则：ORDER_ + yyyyMMddHHmmss + 3位随机数
            order.setOrderNo(OrderNoUtils.getOrderNo());
            order.setUserId(userIds.get(random.nextInt(userIds.size()))); // 从现有用户ID中随机选择
            order.setProductId((long) (random.nextInt(100) + 1));
            order.setTotalFee(random.nextInt(50000) + 1000); // 10元到500元
            order.setOrderStatus(statuses[random.nextInt(statuses.length)]);

            // 设置订单类型：如果指定了orderType则使用指定值，否则随机
            if (orderType != null) {
                order.setType(orderType);
            } else {
                order.setType(types[random.nextInt(types.length)]);
            }

            order.setOrderParm("测试规格" + i);
            orders.add(order);

            // 添加小延迟确保订单号唯一性
            try {
                Thread.sleep(1);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        return orders;
    }

    /**
     * 生成测试购物车订单数据
     */
    private List<CartOrder> generateTestCartOrders(Integer cartOrderCount, List<Long> userIds, Random random, Integer orderType) {
        List<CartOrder> orders = new ArrayList<>();
        String[] titles = {"购物车订单"};
        String[] statuses = {"未支付", "已支付", "处理中", "已发货", "已完成", "已取消"};
        Integer[] types = {1, 2, 3}; // 1:五有，2:有礼，3:新研

        for (int i = 1; i <= cartOrderCount; i++) {
            CartOrder order = new CartOrder();
            order.setTitle(titles[random.nextInt(titles.length)]);
            // 使用真实的购物车订单号生成规则：CART_ORDER_ + yyyyMMddHHmmss + 3位随机数
            order.setOrderNo(OrderNoUtils.getCartOrderNo());
            order.setUserId(userIds.get(random.nextInt(userIds.size()))); // 从现有用户ID中随机选择
            order.setTotalFee(random.nextInt(100000) + 2000); // 20元到1000元
            order.setOrderStatus(statuses[random.nextInt(statuses.length)]);

            // 设置订单类型：如果指定了orderType则使用指定值，否则随机
            if (orderType != null) {
                order.setType(orderType);
            } else {
                order.setType(types[random.nextInt(types.length)]);
            }

            order.setItemCount(random.nextInt(5) + 1); // 1-5种商品
            order.setTotalQuantity(random.nextInt(10) + 1); // 1-10个商品
            orders.add(order);

            // 添加小延迟确保订单号唯一性
            try {
                Thread.sleep(1);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        return orders;
    }

    @Override
    @Transactional
    public String createTestOrder(String orderNo, Long userId, Integer totalFee, java.time.LocalDateTime createTime,
                                 Integer type, String title, Long productId, String orderParm, String remark) {
        logger.info("开始创建测试订单: orderNo={}, userId={}, totalFee={}, type={}", orderNo, userId, totalFee, type);

        try {
            // 验证订单类型
            if (type == null || type < 1 || type > 3) {
                throw new IllegalArgumentException("订单类型必须是1(五有)、2(有礼)或3(新研)");
            }

            // 验证用户是否存在
            List<Long> existingUserIds = testOrderRepository.selectExistingTestUserIds();
            if (!existingUserIds.contains(userId)) {
                throw new IllegalArgumentException("用户ID不存在，请确保用户在wy_user_test表中存在");
            }

            // 构建订单对象
            OrderInfo order = new OrderInfo();
            order.setOrderNo(orderNo);
            order.setUserId(userId);
            order.setTotalFee(totalFee);
            order.setType(type);
            order.setOrderStatus("支付成功"); // 默认状态为支付成功

            // 设置可选字段
            order.setTitle(title != null ? title : getDefaultTitle(type));
            order.setProductId(productId != null ? productId : 1L);
            order.setOrderParm(orderParm != null ? orderParm : "默认规格");
            order.setRemark(remark);
            order.setCreateTime(createTime != null ? createTime : java.time.LocalDateTime.now());

            // 插入订单
            int result = testOrderRepository.insertTestOrder(order);

            if (result > 0) {
                String successMsg = String.format("成功创建测试订单: %s", orderNo);
                logger.info(successMsg);
                return successMsg;
            } else {
                throw new RuntimeException("插入订单失败");
            }

        } catch (Exception e) {
            logger.error("创建测试订单失败", e);
            throw new RuntimeException("创建测试订单失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单类型获取默认标题
     */
    private String getDefaultTitle(Integer type) {
        switch (type) {
            case 1:
                return "五有测试订单";
            case 2:
                return "有礼测试订单";
            case 3:
                return "新研测试订单";
            default:
                return "测试订单";
        }
    }
}
