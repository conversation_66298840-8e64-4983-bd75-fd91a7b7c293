<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="44b71d58-dae5-43e1-a3a3-f2ea3b3b11ba" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/AugmentWebviewStateStore.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/CopilotChatHistory.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/CopilotWebChatHistory.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/encodings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/jarRepositories.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/潼南五有.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo.zip" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/.idea/AugmentWebviewStateStore.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/.idea/CopilotChatHistory.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/.idea/CopilotWebChatHistory.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/.idea/compiler.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/.idea/encodings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/.idea/jarRepositories.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/.idea/misc.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/.idea/payment-demo.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/apiclient_key.pem" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/pages/index/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/pages/index/index.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/pages/index/index.wxml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/pages/index/index.wxss" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/PaymentDemoApplication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/config/MiniProgramConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/config/MyBatisPlusConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/config/Swagger2Config.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/config/WebConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/config/WxPayConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/controller/OrderInfoController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/controller/ProductController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/controller/TestController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/controller/WxLoginController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/controller/WxPayController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/controller/WxPayV2Controller.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/entity/BaseEntity.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/entity/OrderInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/entity/OrderItem.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/entity/PayParm.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/entity/PaymentInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/entity/Product.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/entity/RefundInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/entity/ResearchActivity.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/entity/Sku.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/entity/User.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/entity/UserBehaviorStats.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/entity/WyAdoptionPackage.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/enums/OrderStatus.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/enums/PayType.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/enums/wxpay/WxApiType.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/enums/wxpay/WxNotifyType.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/enums/wxpay/WxRefundStatus.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/enums/wxpay/WxTradeState.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/mapper/OrderInfoMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/mapper/OrderItemMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/mapper/PaymentInfoMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/mapper/ProductMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/mapper/RefundInfoMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/mapper/ResearchActivityRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/mapper/SkuMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/mapper/UserBehaviorStatsRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/mapper/UserMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/mapper/WyAdoptionPackageRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/mapper/xml/OrderInfoMapper.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/mapper/xml/PaymentInfoMapper.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/mapper/xml/ProductMapper.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/mapper/xml/RefundInfoMapper.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/service/MiniProgramService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/service/OrderInfoService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/service/PaymentInfoService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/service/ProductService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/service/RefundInfoService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/service/WxPayService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/service/impl/MiniProgramServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/service/impl/OrderInfoServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/service/impl/PaymentInfoServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/service/impl/ProductServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/service/impl/RefundInfoServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/service/impl/WxPayServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/task/WxPayTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/util/Base64Utils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/util/HttpClientUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/util/HttpUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/util/OrderNoUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/util/WechatPay2ValidatorForRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/util/WechatPayUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/vo/R.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/resources/apiclient_cert.p12" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/resources/apiclient_key.pem" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/resources/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/resources/static/mini-pay-demo.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/main/resources/wxpay.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/src/test/java/com/starlight/paymentdemo/PaymentDemoApplicationTests.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/apiclient_cert.p12" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/apiclient_key.pem" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/PaymentDemoApplication.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/config/MiniProgramConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/config/MyBatisPlusConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/config/Swagger2Config.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/config/WebConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/config/WxPayConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/controller/OrderInfoController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/controller/ProductController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/controller/TestController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/controller/WxLoginController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/controller/WxPayController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/controller/WxPayV2Controller.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/entity/BaseEntity.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/entity/OrderInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/entity/OrderItem.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/entity/PayParm.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/entity/PaymentInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/entity/Product.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/entity/RefundInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/entity/ResearchActivity.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/entity/Sku.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/entity/User.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/entity/UserBehaviorStats.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/entity/WyAdoptionPackage.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/enums/OrderStatus.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/enums/PayType.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/enums/wxpay/WxApiType.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/enums/wxpay/WxNotifyType.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/enums/wxpay/WxRefundStatus.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/enums/wxpay/WxTradeState.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/mapper/OrderInfoMapper.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/mapper/OrderItemMapper.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/mapper/PaymentInfoMapper.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/mapper/ProductMapper.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/mapper/RefundInfoMapper.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/mapper/ResearchActivityRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/mapper/SkuMapper.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/mapper/UserBehaviorStatsRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/mapper/UserMapper.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/mapper/WyAdoptionPackageRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/mapper/xml/OrderInfoMapper.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/mapper/xml/PaymentInfoMapper.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/mapper/xml/ProductMapper.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/mapper/xml/RefundInfoMapper.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/service/MiniProgramService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/service/OrderInfoService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/service/PaymentInfoService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/service/ProductService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/service/RefundInfoService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/service/WxPayService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/service/impl/MiniProgramServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/service/impl/OrderInfoServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/service/impl/PaymentInfoServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/service/impl/ProductServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/service/impl/RefundInfoServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/service/impl/WxPayServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/task/WxPayTask.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/util/Base64Utils.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/util/HttpClientUtils.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/util/HttpUtils.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/util/OrderNoUtils.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/util/WechatPay2ValidatorForRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/util/WechatPayUtil.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/com/starlight/paymentdemo/vo/R.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/static/mini-pay-demo.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/classes/wxpay.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/maven-archiver/pom.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/createdFiles.lst" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/inputFiles.lst" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/payment-demo-0.0.1-SNAPSHOT.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/payment-demo-0.0.1-SNAPSHOT.jar.original" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/payment-demo/target/test-classes/com/starlight/paymentdemo/PaymentDemoApplicationTests.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project.zip" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/.idea/AugmentWebviewStateStore.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/.idea/CopilotChatHistory.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/.idea/CopilotWebChatHistory.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/.idea/compiler.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/.idea/encodings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/.idea/jarRepositories.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/.idea/misc.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/.idea/uiDesigner.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/.vscode/settings.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/1.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/JAVA-Desc2.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/doc.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/index.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/manage.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/五有后台/溯源/摄像头.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/五有后台/溯源/溯源信息.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/五有后台/溯源/生产过程.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/五有后台/溯源/设备.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/五有后台/示范点管理API文档.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/五有后台/认养模版/认养方案/二级/常见问题.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/五有后台/认养模版/认养方案/二级/收获周期.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/五有后台/认养模版/认养方案/二级/权益列表.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/五有后台/认养模版/认养方案/认养方案.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/五有后台/认养模版/认养方案模版.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/我的/wy_points_rule.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/我的/会员管理.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/我的/会员管理扩展.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/我的/成就管理.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/我的/成就管理.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/我的/用户.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/我的/积分管理.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/统计分析/收入来源配置表.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/统计分析/用户表.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/统计分析/用户表扩展.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/统计分析/用户访问日志表.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/统计分析/用户预约表.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/统计分析/统计分析.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/统计分析/统计分析汇总表.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/统计分析/订单表.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/业务api/订单/订单管理.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/接口文档提示词.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/API/接口文档模版.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/sql设计/sql创建提示词.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/sql设计/业务/deduction_record.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/sql设计/业务/五有后台/示范点.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/sql设计/业务/五有后台/订单管理/地址.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/sql设计/业务/五有后台/订单管理/用户.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/sql设计/业务/五有后台/订单管理/订单.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/sql设计/业务/五有后台/认养模版/认养方案/二级/常见问题.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/sql设计/业务/五有后台/认养模版/认养方案/二级/收获周期.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/sql设计/业务/五有后台/认养模版/认养方案/二级/权益列表.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/sql设计/业务/五有后台/认养模版/认养方案/认养方案表.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/sql设计/业务/五有后台/认养模版/认养方案模版表.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/sql设计/业务/五有后台/首页配置/首页配置.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/sql设计/数据库提示词.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/prompt/后端/代码编写提示.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/public/product/manage.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/public/product/product-manage.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/sql/cart_order_tables.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/FengDuStarApplication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/annotation/RateLimit.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/common/ActivityApiResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/common/ApiResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/common/BaseController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/common/GlobalExceptionHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/common/JsonUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/common/OrderApiResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/common/PageResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/common/TraceabilityApiResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/config/CorsConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/config/JacksonConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/config/LocalDateTimeConverter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/config/MyBatisPlusConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/config/RedisConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/config/SwaggerConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/config/WebMvcConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/AchievementController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/ActivityController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/AdoptionPackageController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/AdoptionPlanBenefitController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/AdoptionTemplateController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/CameraController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/CartOrderController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/DemoSiteController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/DeviceController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/FaqController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/FiveGoodController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/HarvestPeriodController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/MemberAchievementController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/MemberController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/OrderController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/PointsManagementController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/ProductionRecordController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/StatisticsController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/TraceabilityInfoController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/admin/BannerController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/admin/HotTopicController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/controller/api/HomeConfigController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/dto/AdoptionTemplateDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/dto/AdoptionTemplateQueryDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/dto/DeductionRecordQueryDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/dto/DemoSiteDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/AchievementRule.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/Activity.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/AdoptionPackage.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/AdoptionPlanBenefit.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/AdoptionTemplate.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/Banner.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/Camera.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/CartOrder.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/CartOrderItem.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/Category.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/DemoSite.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/Device.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/Faq.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/FiveGood.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/HarvestPeriod.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/HotTopic.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/OrderAddress.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/OrderInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/ProductionRecord.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/ReservationQuery.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/RevenueSource.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/StatisticsSummary.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/Task.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/TraceabilityInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/User.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/UserAchievement.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/UserAddress.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/UserBehaviorStats.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/UserPoints.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/UserVisitLog.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/WyMemberConsumptionLog.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/WyMemberLevelRules.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/WyMemberPointsLog.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/WyPointsRule.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/WyUser.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/AchievementListDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/AchievementPageResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/AchievementRulesDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/ActivityDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/ActivityPageResponseDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/ActivitySearchResponseDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/AdoptionPackageDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/AdoptionPlanDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/BatchDeleteRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/BatchUpdateStatusDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/BenefitDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/BenefitUpdateDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/CameraCreateDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/CameraResponseDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/CameraUpdateDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/CartOrderDetailResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/CartOrderResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/CategoryDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/DeviceDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/FaqDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/HarvestPeriodDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/MemberAchievementDetailDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/MemberDetailDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/MemberLevelCheckDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/MemberLevelRuleDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/MemberListDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/MemberSimpleDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/MemberUpdateDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/OrderDetailDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/OrderEditDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/OrderListDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/PointsAdjustRequestDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/PointsAdjustResponseDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/PointsRecordDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/PointsRuleDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/ProductDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/ProductionRecordCreateDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/ProductionRecordDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/ProductionRecordPageDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/ProductionRecordUpdateDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/RevenueSourceDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/RevenueTrendDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/StatisticsOverviewDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/TraceabilityInfoPageDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/UserActivityDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/UserGrowthDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/entity/dto/UserRegionDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/exception/AuthExceptionHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/exception/RateLimitException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/exception/UnauthorizedException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/filter/GlobalExceptionFilter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/interceptor/RateLimitInterceptor.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/AchievementRuleRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/ActivityRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/AdoptionPackageRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/AdoptionPlanBenefitRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/AdoptionTemplateRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/BannerRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/CameraRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/CartOrderItemRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/CartOrderRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/CategoryRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/DemoSiteRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/DeviceRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/FaqRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/FiveGoodRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/HarvestPeriodRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/HotTopicRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/OrderAddressRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/OrderInfoRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/ProductionRecordRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/RevenueSourceRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/StatisticsSummaryRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/TaskRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/TraceabilityInfoRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/UserAchievementRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/UserAddressRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/UserBehaviorStatsRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/UserPointsRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/UserRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/UserVisitLogRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/WyMemberLevelRulesRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/WyMemberPointsLogRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/WyPointsRuleRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/repository/WyUserRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/AchievementService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/ActivityService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/AdoptionPackageService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/AdoptionPlanBenefitService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/AdoptionTemplateService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/BannerService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/CameraService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/CartOrderService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/CategoryService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/DemoSiteService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/DeviceService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/FaqService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/FiveGoodService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/HarvestPeriodService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/HotTopicService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/MemberService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/OrderAddressService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/OrderInfoService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/PointsManagementService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/ProductionRecordService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/StatisticsService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/TraceabilityInfoService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/UserAddressService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/UserService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/AchievementServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/ActivityServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/AdoptionPackageServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/AdoptionPlanBenefitServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/AdoptionTemplateServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/BannerServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/CameraServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/CartOrderServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/CategoryServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/DemoSiteServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/DeviceServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/FaqServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/FiveGoodServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/HarvestPeriodServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/HotTopicServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/MemberServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/OrderAddressServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/OrderInfoServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/PointsManagementServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/ProductionRecordServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/StatisticsServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/TraceabilityInfoServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/UserAddressServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/service/impl/UserServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/task/CountTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/task/GiftExpireTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/util/OrderNoUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/util/OrderStatusUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/utils/CameraConverter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/com/miniot/fengdu/utils/HtmlUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/java/doc.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/resources/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/src/main/resources/sql/create_banner_and_hot_topic_tables.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/FengDuStarApplication.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/annotation/RateLimit.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/common/ActivityApiResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/common/ApiResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/common/BaseController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/common/GlobalExceptionHandler.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/common/JsonUtil.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/common/OrderApiResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/common/PageResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/common/TraceabilityApiResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/config/CorsConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/config/LocalDateTimeConverter.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/config/MyBatisPlusConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/config/RedisConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/config/SwaggerConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/config/WebMvcConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/AchievementController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/ActivityController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/AdoptionPackageController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/AdoptionPlanBenefitController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/AdoptionTemplateController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/CameraController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/CartOrderController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/DemoSiteController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/DeviceController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/FaqController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/FiveGoodController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/HarvestPeriodController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/MemberAchievementController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/MemberController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/OrderController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/PointsManagementController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/ProductionRecordController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/StatisticsController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/TraceabilityInfoController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/admin/BannerController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/admin/HotTopicController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/controller/api/HomeConfigController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/dto/AdoptionTemplateDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/dto/AdoptionTemplateQueryDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/dto/DeductionRecordQueryDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/dto/DemoSiteDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/AchievementRule.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/Activity.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/AdoptionPackage.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/AdoptionPlanBenefit.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/AdoptionTemplate.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/Banner.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/Camera.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/CartOrder$OrderStatus.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/CartOrder$OrderType.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/CartOrder.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/CartOrderItem.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/Category.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/DemoSite.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/Device.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/Faq.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/FiveGood.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/HarvestPeriod.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/HotTopic.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/OrderAddress.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/OrderInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/ProductionRecord.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/ReservationQuery.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/RevenueSource.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/StatisticsSummary.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/Task.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/TraceabilityInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/User.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/UserAchievement.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/UserAddress.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/UserBehaviorStats.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/UserPoints.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/UserVisitLog.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/WyMemberConsumptionLog.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/WyMemberLevelRules.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/WyMemberPointsLog.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/WyPointsRule.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/WyUser.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/AchievementListDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/AchievementPageResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/AchievementRulesDTO$AchievementRuleType.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/AchievementRulesDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/ActivityDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/ActivityPageResponseDTO$ActivityPageDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/ActivityPageResponseDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/ActivitySearchResponseDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/AdoptionPackageDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/AdoptionPlanDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/BatchDeleteRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/BatchUpdateStatusDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/BenefitDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/BenefitUpdateDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/CameraCreateDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/CameraResponseDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/CameraUpdateDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/CartOrderDetailResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/CartOrderResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/CategoryDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/DeviceDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/FaqDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/HarvestPeriodDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/MemberAchievementDetailDTO$AchievementItem.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/MemberAchievementDetailDTO$AchievementTypeDetail.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/MemberAchievementDetailDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/MemberDetailDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/MemberLevelCheckDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/MemberLevelRuleDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/MemberListDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/MemberSimpleDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/MemberUpdateDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/OrderDetailDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/OrderEditDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/OrderListDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/PointsAdjustRequestDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/PointsAdjustResponseDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/PointsRecordDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/PointsRuleDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/ProductDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/ProductionRecordCreateDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/ProductionRecordDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/ProductionRecordPageDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/ProductionRecordUpdateDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/RevenueSourceDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/RevenueTrendDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/StatisticsOverviewDto$MetricDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/StatisticsOverviewDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/TraceabilityInfoPageDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/UserActivityDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/UserGrowthDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/entity/dto/UserRegionDto.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/exception/AuthExceptionHandler.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/exception/RateLimitException.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/exception/UnauthorizedException.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/interceptor/RateLimitInterceptor.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/AchievementRuleRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/ActivityRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/AdoptionPackageRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/AdoptionPlanBenefitRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/AdoptionTemplateRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/BannerRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/CameraRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/CartOrderItemRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/CartOrderRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/CategoryRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/DemoSiteRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/DeviceRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/FaqRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/FiveGoodRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/HarvestPeriodRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/HotTopicRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/OrderAddressRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/OrderInfoRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/ProductionRecordRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/RevenueSourceRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/StatisticsSummaryRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/TaskRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/TraceabilityInfoRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/UserAchievementRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/UserAddressRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/UserBehaviorStatsRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/UserPointsRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/UserRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/UserVisitLogRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/WyMemberLevelRulesRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/WyMemberPointsLogRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/WyPointsRuleRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/repository/WyUserRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/AchievementService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/ActivityService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/AdoptionPackageService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/AdoptionPlanBenefitService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/AdoptionTemplateService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/BannerService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/CameraService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/CartOrderService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/CategoryService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/DemoSiteService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/DeviceService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/FaqService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/FiveGoodService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/HarvestPeriodService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/HotTopicService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/MemberService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/OrderAddressService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/OrderInfoService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/PointsManagementService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/ProductionRecordService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/StatisticsService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/TraceabilityInfoService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/UserAddressService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/UserService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/AchievementServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/ActivityServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/AdoptionPackageServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/AdoptionPlanBenefitServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/AdoptionTemplateServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/BannerServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/CameraServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/CartOrderServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/CategoryServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/DemoSiteServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/DeviceServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/FaqServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/FiveGoodServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/HarvestPeriodServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/HotTopicServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/MemberServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/OrderAddressServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/OrderInfoServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/PointsManagementServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/ProductionRecordServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/StatisticsServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/TraceabilityInfoServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/UserAddressServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/service/impl/UserServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/task/CountTask.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/task/GiftExpireTask.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/util/OrderNoUtils.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/util/OrderStatusUtils$CartOrderStatus.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/util/OrderStatusUtils$PaymentOrderStatus.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/util/OrderStatusUtils$SingleOrderStatus.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/util/OrderStatusUtils.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/utils/CameraConverter.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/com/miniot/fengdu/utils/HtmlUtils.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/doc.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/classes/sql/create_banner_and_hot_topic_tables.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/maven-archiver/pom.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/createdFiles.lst" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/inputFiles.lst" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/wuyou-back-project-1.0-SNAPSHOT.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/target/wuyou-back-project-1.0-SNAPSHOT.jar.original" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/temp/接口文档模版.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/test.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/wy_five_good.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/研学活动模块说明.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/积分规则表创建脚本.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/设计/api/@首页配置.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/设计/api/api.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/设计/sql/database.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project/设计/sql/sql.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序.zip" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/.idea/CopilotChatHistory.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/.idea/CopilotWebChatHistory.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/.idea/compiler.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/.idea/encodings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/.idea/jarRepositories.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/.idea/misc.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/.idea/uiDesigner.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/.idea/wuyou-project小程序.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/.vscode/settings.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/JAVA-Desc.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/JAVA-Desc2.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/五有/【待确认】我的认养模块-我的赠送.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/五有/五有api.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/五有/我的认养模块-我的认养.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/五有/溯源.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/五有/认养方案按钮.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/我的/我的.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/我的/我的服务/我的反馈.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/我的/我的服务/联系客服.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/我的/我的服务/设置-基本信息.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/我的/我的服务/设置.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/我的/我的权益/成就.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/我的/我的权益/活动收获.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/我的/我的权益/积分.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/我的/我的订单/订单.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/我的/查看按钮.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/我的/赠送按钮.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/新研.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/有礼.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/用户.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/订单.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/通用/地址管理/地址.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/首页/内容/关注与取消关注.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/首页/内容/发布内容.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/首页/内容/点赞.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/首页/内容/用户详情数据.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/首页/内容/评论.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/首页/试吃.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/API/首页api.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/API/接口文档提示词.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/API/接口文档模版.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/sql设计/sql创建提示词.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/sql设计/地址表设计.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/数据库提示词.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/五有资源表 (wy_resource).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/套餐权益表 (wy_package_benefit).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/常见问题表 (wy_faq).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/我的服务/客服/customer_service_faq.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/我的服务/客服/customer_service_info.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/我的服务/用户反馈.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/收获周期表 (wy_harvest_period).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/权益/成就/achievement_badges.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/权益/成就/achievement_point_history.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/权益/成就/user_achievements.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/权益/成就/user_badge_progress.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/权益/活动收获表.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/权益/积分表设计.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/溯源/溯源sql.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/生长日志表 (wy_growth_log).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/用户评价表 (wy_user_review).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/示范点表 (wy_demo_site).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/认养套餐表 (wy_adoption_package).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/认养订单表 (wy_adoption_order).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/认养项目表 (wy_adoption_project).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/赠送规则表 (wy_gift_rule).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/赠送记录表 (wy_gift_record).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/五有/项目监控数据表 (wy_project_monitor).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/微信支付/doc.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/微信支付/微信支付配置表 (wy_wx_pay_config).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/微信支付/支付回调记录表 (wy_payment_notify).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/微信支付/支付日志表 (wy_payment_log).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/微信支付/支付记录表 (wy_payment_record).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/微信支付/用户微信信息表 (wy_user_wechat).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/微信支付/退款记录表 (wy_refund_record).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/有礼/product.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/用户关注表(user_follow).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/用户标签表(user_tag).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/用户表(user).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/研学活动表.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/订单.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/购物车/shopping_cart.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/购物车/表结构对照.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/首页/促销商品表 (promotion).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/首页/内容表 (content).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/首页/分类表 (category).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/首页/用户关注表 (user_follow).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/首页/用户点赞表 (user_like).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/首页/系统配置表 (config).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/首页/评论表.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/首页/试吃活动表.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/mysql/项目sql/首页/轮播图表 (banner).sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/design/temp/后端/代码编写提示.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/doc.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/index.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/manage.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/prompt/API/业务api/五有后台/示范点管理API文档.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/prompt/API/接口文档提示词.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/prompt/API/接口文档模版.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/prompt/sql设计/sql创建提示词.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/prompt/sql设计/业务/deduction_record.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/prompt/sql设计/业务/五有后台/示范点.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/prompt/sql设计/数据库提示词.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/prompt/后端/代码编写提示.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/public/product/manage.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/public/product/product-manage.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/FengDuStarApplication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/FengduApplication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/annotation/RateLimit.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/common/ApiResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/common/BaseController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/common/GlobalExceptionHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/common/JsonUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/common/PageResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/config/ContentConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/config/CorsConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/config/JacksonConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/config/LocalDateTimeConverter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/config/MyBatisPlusConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/config/PaymentServiceConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/config/RedisConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/config/RestTemplateConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/config/SwaggerConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/config/WebMvcConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/AchievementController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/ActivityHarvestController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/AdoptionController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/AdoptionGiftController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/CartOrderController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/CommentController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/ContentController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/CustomerServiceController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/FeedbackController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/GoodsController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/HomeController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/MonitorDeviceController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/OrderController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/PageController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/PaymentNotifyController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/PointsController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/ProductAdminController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/ProductController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/PromotionController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/RegionController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/ResearchActivityController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/ReservationQueryController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/ShoppingCartController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/TasteActivityController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/TraceabilityController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/UserAddressController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/UserController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/UserFollowController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/UserProfileController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/WxReservationQueryController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/WyAdoptionController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/WyAdoptionOrderController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/WyAdoptionProjectController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/WyDemoSiteController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/controller/WyResourceController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/dto/DeductionRecordQueryDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/dto/DemoSiteDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/AchievementBadge.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/AchievementPointHistory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/AchievementRule.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/ActivityCertificate.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/ActivityHarvest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/ActivityItem.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/AdoptionPlanBenefit.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/AdoptionTemplate.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/Banner.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/Camera.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/CartOrder.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/CartOrderItem.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/Category.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/CategoryGoods.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/Certification.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/City.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/Comment.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/Config.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/Content.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/ContentLike.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/CustomerServiceFaq.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/CustomerServiceInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/DemoSite.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/District.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/FeedbackReply.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/FeedbackType.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/FiveGood.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/LogisticsTracking.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/MemberLevel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/MonitorDevice.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/OrderAddress.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/OrderInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/OrderItem.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/OrderLogistics.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/PointsRecord.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/Product.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/ProductTag.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/ProductionRecord.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/Promotion.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/PromotionType.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/Province.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/ResearchActivity.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/ResearchActivityImage.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/ResearchActivityNotice.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/ResearchActivityReward.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/ResearchActivitySchedule.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/Reservation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/ReservationQuery.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/ShoppingCart.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/Sku.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/Spec.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/Spu.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/TasteActivity.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/TasteApplication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/TraceabilityInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/TraceabilityShare.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/User.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/UserAchievement.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/UserActivityItem.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/UserAddress.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/UserBadgeProgress.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/UserBehaviorStats.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/UserFeedback.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/UserFollow.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/UserLike.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/UserPoints.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/WyAdoptionOrder.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/WyAdoptionPackage.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/WyAdoptionProject.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/WyDemoSite.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/WyFaq.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/WyGiftRecord.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/WyGiftRule.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/WyGrowthLog.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/WyHarvestPeriod.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/WyMemberLevelRules.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/WyMemberPointsLog.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/WyPackageBenefit.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/WyResource.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/WyUserReview.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/AchievementDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/AchievementHistoryDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/AchievementInfoDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/AchievementListDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ActivityCertificateDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ActivityHarvestDetailDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ActivityHarvestItemDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ActivityHarvestListResponseDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ActivityHarvestRecordDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ActivityHarvestStatsDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/AddPointsRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/AddressRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/AddressResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/AdoptionDetailResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/AdoptionProjectResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/AdoptionStatsResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/BadgeProgressUpdateDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/BannerResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartCheckoutPreviewRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartCheckoutPreviewResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartCheckoutRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartCheckoutResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartClearResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartOrderCancelRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartOrderDetailResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartOrderExpressUpdateRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartOrderItemDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartOrderListResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartOrderPaymentStatusResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartOrderPaymentUpdateRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartOrderRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartOrderResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartRemoveRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartRemoveResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartSelectAllRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartSelectRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartSelectResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CartUpdateQuantityRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CategoryResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CheckInRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CityDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CommentDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CommentUserDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ConfigResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ContentDetailResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ContentPublishRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ContentPublishResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ContentResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ContentWithFollow.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CustomerServiceFaqDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/CustomerServiceInfoDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/FeedbackDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/FeedbackReplyDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/FeedbackSubmitDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/FeedbackTypeDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/GiftConfirmRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/GiftInfoResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/GiftRecordListResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/GiftRuleResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/GiftSubmitRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/GiftSubmitResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/GrowthLogResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/HomeProductResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/LevelThresholdDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/LogisticsTrackingDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/MonitorDeviceListResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/MonitorDevicePageResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/MonitorDeviceRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/MonitorResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/MyAdoptionResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/OrderAddressDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/OrderCancelRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/OrderConfirmRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/OrderDetailResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/OrderItemDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/OrderListResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/OrderLogisticsDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/PointsRecordDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ProductDetailResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ProductRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ProductResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/PromotionResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/PromotionTypeResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ProvinceDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/RegionDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ResearchActivityDetailResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ResearchActivityListResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ReservationQueryCreateRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ReservationQueryListResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ReservationQueryResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ReservationRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ReservationResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ShoppingCartListResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ShoppingCartRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/ShoppingCartResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/SkuDetailResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/TasteActivityDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/TasteApplicationDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/TasteApplicationRequestDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/TasteApplicationResponseDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/UserFollowResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/UserLikeesponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/UserLoginRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/UserPointsInfoDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/UserProfileResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/UserProfileUpdateRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/UserRegisterRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/UserResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/UserTagResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/UserUpdateRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/WyAdoptionOrderRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/WyAdoptionOrderResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/WyAdoptionPlanResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/WyDemoSiteDetailResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/WyDemoSiteListResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/WyFaqResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/WyPackageTypeResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/WyResourceResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/WyUserReviewPageResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/WyUserReviewResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/home/<USER>" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/home/<USER>" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/home/<USER>" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/traceability/MonitorSYResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/traceability/ShareRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/traceability/ShareResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/traceability/TraceabilityDetailResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/entity/dto/traceability/TraceabilityListResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/exception/AuthExceptionHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/exception/RateLimitException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/exception/UnauthorizedException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/filter/GlobalExceptionFilter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/interceptor/RateLimitInterceptor.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/AchievementBadgeRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/AchievementPointHistoryRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/AchievementRuleRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ActivityCertificateRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ActivityHarvestRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ActivityItemRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/AdoptionTemplateRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/BannerRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/CameraRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/CartOrderItemRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/CartOrderRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/CategoryGoodsRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/CategoryRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/CertificationRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/CityRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/CommentRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ConfigRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ContentRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/CustomerServiceFaqRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/CustomerServiceInfoRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/DemoSiteRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/DistrictRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/FeedbackReplyRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/FeedbackTypeRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/FiveGoodRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/LogisticsTrackingRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/MemberLevelRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/MonitorDeviceRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/OrderAddressRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/OrderInfoRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/OrderItemRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/OrderLogisticsRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/PointsRecordRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ProductRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ProductTagRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ProductionRecordRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/PromotionRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/PromotionTypeRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ProvinceRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ResearchActivityImageRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ResearchActivityNoticeRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ResearchActivityRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ResearchActivityRewardRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ResearchActivityScheduleRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ReservationQueryRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ReservationRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/ShoppingCartRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/SkuRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/SpecRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/SpuRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/TasteActivityRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/TasteApplicationRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/TraceabilityInfoRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/TraceabilityShareRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/UserAchievementRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/UserActivityItemRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/UserAddressRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/UserBadgeProgressRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/UserBehaviorStatsRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/UserFeedbackRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/UserFollowRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/UserLikeRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/UserPointsRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/UserRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/WyAdoptionOrderRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/WyAdoptionPackageRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/WyAdoptionProjectRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/WyDemoSiteRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/WyFaqRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/WyGiftRecordRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/WyGiftRuleRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/WyGrowthLogRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/WyHarvestPeriodRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/WyMemberLevelRulesRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/WyMemberPointsLogRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/WyPackageBenefitRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/WyResourceRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/WyUserRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/repository/WyUserReviewRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/AchievementService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/ActivityHarvestService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/AdoptionGiftService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/AdoptionProjectService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/AdoptionService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/AuthService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/BackendApiService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/BannerService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/CartOrderService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/CategoryService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/CommentService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/ConfigService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/ContentService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/CustomerServiceService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/DemoSiteService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/FeedbackService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/FiveGoodService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/MonitorDeviceService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/OrderService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/PaymentApiService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/PointsService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/ProductService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/PromotionService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/PromotionTypeService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/RegionService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/ResearchActivityService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/ReservationQueryService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/ReservationService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/ShoppingCartService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/TasteActivityService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/TraceabilityService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/UserAddressService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/UserFollowService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/UserLikeService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/UserProfileService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/UserService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/WyAdoptionOrderService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/WyAdoptionPackageService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/WyDemoSiteService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/WyFaqService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/WyResourceService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/WyUserReviewService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/AchievementServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/ActivityHarvestServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/AdoptionGiftServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/AdoptionProjectServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/AdoptionServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/AuthServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/BackendApiServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/BannerServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/CartOrderServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/CategoryServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/CommentServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/ConfigServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/ContentServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/CustomerServiceServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/FeedbackServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/MonitorDeviceServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/OrderServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/PaymentApiServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/PointsServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/ProductServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/PromotionServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/PromotionTypeServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/RegionServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/ResearchActivityServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/ReservationQueryServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/ReservationServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/ShoppingCartServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/TasteActivityServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/TraceabilityServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/UserAddressServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/UserFollowServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/UserLikeServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/UserProfileServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/UserServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/WyAdoptionOrderServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/WyAdoptionPackageServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/WyDemoSiteServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/WyFaqServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/WyResourceServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/service/impl/WyUserReviewServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/task/GiftExpireTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/util/OrderNoUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/utils/HtmlUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/com/miniot/fengdu/utils/PriceConverterUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/java/doc.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/src/main/resources/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/FengDuStarApplication.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/annotation/RateLimit.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/common/ApiResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/common/BaseController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/common/GlobalExceptionHandler.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/common/JsonUtil.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/common/PageResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/config/ContentConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/config/CorsConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/config/LocalDateTimeConverter.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/config/MyBatisPlusConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/config/PaymentServiceConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/config/RedisConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/config/RestTemplateConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/config/SwaggerConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/config/WebMvcConfig.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/AchievementController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/ActivityHarvestController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/AdoptionController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/AdoptionGiftController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/CartOrderController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/CommentController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/ContentController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/CustomerServiceController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/FeedbackController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/GoodsController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/HomeController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/MonitorDeviceController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/OrderController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/PageController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/PaymentNotifyController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/PointsController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/ProductAdminController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/ProductController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/PromotionController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/RegionController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/ResearchActivityController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/ReservationQueryController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/ShoppingCartController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/TasteActivityController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/TraceabilityController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/UserAddressController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/UserController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/UserFollowController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/UserProfileController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/WxReservationQueryController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/WyAdoptionController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/WyAdoptionOrderController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/WyAdoptionProjectController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/WyDemoSiteController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/controller/WyResourceController.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/dto/DeductionRecordQueryDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/dto/DemoSiteDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/AchievementBadge.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/AchievementPointHistory.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/AchievementRule.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/ActivityCertificate.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/ActivityHarvest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/ActivityItem.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/AdoptionPlanBenefit.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/AdoptionTemplate.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/Banner.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/Camera.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/CartOrder$OrderStatus.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/CartOrder$OrderType.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/CartOrder$PaymentMethod.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/CartOrder.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/CartOrderItem.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/Category.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/CategoryGoods.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/Certification.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/City.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/Comment.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/Config.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/Content.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/ContentLike.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/CustomerServiceFaq.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/CustomerServiceInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/DemoSite.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/District.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/FeedbackReply.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/FeedbackType.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/FiveGood.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/LogisticsTracking.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/MemberLevel.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/MonitorDevice.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/OrderAddress.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/OrderInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/OrderItem.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/OrderLogistics.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/PointsRecord.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/Product.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/ProductTag.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/ProductionRecord.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/Promotion.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/PromotionType.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/Province.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/ResearchActivity.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/ResearchActivityImage.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/ResearchActivityNotice.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/ResearchActivityReward.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/ResearchActivitySchedule.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/Reservation.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/ReservationQuery.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/ShoppingCart.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/Sku.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/Spec.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/Spu.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/TasteActivity.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/TasteApplication.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/TraceabilityInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/TraceabilityShare.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/User.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/UserAchievement.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/UserActivityItem.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/UserAddress.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/UserBadgeProgress.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/UserBehaviorStats.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/UserFeedback.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/UserFollow.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/UserLike.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/UserPoints.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/WyAdoptionOrder.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/WyAdoptionPackage.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/WyAdoptionProject.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/WyDemoSite.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/WyFaq.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/WyGiftRecord.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/WyGiftRule.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/WyGrowthLog.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/WyHarvestPeriod.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/WyMemberLevelRules.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/WyMemberPointsLog.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/WyPackageBenefit.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/WyResource.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/WyUserReview.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/AchievementDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/AchievementHistoryDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/AchievementInfoDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/AchievementListDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ActivityCertificateDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ActivityHarvestDetailDTO$CertificateDetailDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ActivityHarvestDetailDTO$ItemDetailDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ActivityHarvestDetailDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ActivityHarvestItemDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ActivityHarvestListResponseDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ActivityHarvestRecordDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ActivityHarvestStatsDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/AddPointsRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/AddressRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/AddressResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/AdoptionDetailResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/AdoptionProjectResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/AdoptionStatsResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/BadgeProgressUpdateDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/BannerResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartCheckoutPreviewRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartCheckoutPreviewResponse$AddressInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartCheckoutPreviewResponse$CheckoutItem.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartCheckoutPreviewResponse$CouponInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartCheckoutPreviewResponse$PriceInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartCheckoutPreviewResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartCheckoutRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartCheckoutResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartClearResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartOrderCancelRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartOrderDetailResponse$AddressInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartOrderDetailResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartOrderExpressUpdateRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartOrderItemDTO$SalesStats.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartOrderItemDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartOrderListResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartOrderPaymentStatusResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartOrderPaymentUpdateRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartOrderRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartOrderResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartRemoveRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartRemoveResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartSelectAllRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartSelectRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartSelectResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CartUpdateQuantityRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CategoryResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CheckInRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CityDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CommentDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CommentUserDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ConfigResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ContentDetailResponse$AuthorInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ContentDetailResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ContentPublishRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ContentPublishResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ContentResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ContentWithFollow.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CustomerServiceFaqDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CustomerServiceInfoDTO$FeedbackInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CustomerServiceInfoDTO$HotlineInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/CustomerServiceInfoDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/FeedbackDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/FeedbackReplyDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/FeedbackSubmitDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/FeedbackTypeDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/GiftConfirmRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/GiftInfoResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/GiftRecordListResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/GiftRuleResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/GiftSubmitRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/GiftSubmitResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/GrowthLogResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/HomeProductResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/LevelThresholdDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/LogisticsTrackingDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/MonitorDeviceListResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/MonitorDevicePageResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/MonitorDeviceRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/MonitorResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/MyAdoptionResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/OrderAddressDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/OrderCancelRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/OrderConfirmRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/OrderDetailResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/OrderItemDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/OrderListResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/OrderLogisticsDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/PointsRecordDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ProductDetailResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ProductRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ProductResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/PromotionResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/PromotionTypeResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ProvinceDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/RegionDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ResearchActivityDetailResponse$RewardItem.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ResearchActivityDetailResponse$ScheduleItem.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ResearchActivityDetailResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ResearchActivityListResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ReservationQueryCreateRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ReservationQueryListResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ReservationQueryResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ReservationRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ReservationResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ShoppingCartListResponse$CartSummary.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ShoppingCartListResponse$Pagination.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ShoppingCartListResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ShoppingCartRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ShoppingCartResponse$CartSummaryInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/ShoppingCartResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/SkuDetailResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/TasteActivityDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/TasteApplicationDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/TasteApplicationRequestDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/TasteApplicationResponseDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/UserFollowResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/UserLikeesponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/UserLoginRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/UserPointsInfoDTO.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/UserProfileResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/UserProfileUpdateRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/UserRegisterRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/UserResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/UserTagResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/UserUpdateRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyAdoptionOrderRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyAdoptionOrderResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyAdoptionPlanResponse$BenefitInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyAdoptionPlanResponse$HarvestPeriodInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyAdoptionPlanResponse$PackageInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyAdoptionPlanResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyDemoSiteDetailResponse$AdoptionMethod.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyDemoSiteDetailResponse$AdoptionPackage.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyDemoSiteDetailResponse$BasicInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyDemoSiteDetailResponse$SelfMethod.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyDemoSiteDetailResponse$SpecialProducts.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyDemoSiteDetailResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyDemoSiteListResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyFaqResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyPackageTypeResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyResourceResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyUserReviewPageResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/WyUserReviewResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/home/<USER>" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/home/<USER>" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/home/<USER>" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/traceability/MonitorSYResponse$CameraInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/traceability/MonitorSYResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/traceability/ShareRequest.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/traceability/ShareResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/traceability/TraceabilityDetailResponse$BasicInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/traceability/TraceabilityDetailResponse$CertificationInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/traceability/TraceabilityDetailResponse$CropInfo$LocationInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/traceability/TraceabilityDetailResponse$CropInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/traceability/TraceabilityDetailResponse$ProductionRecordInfo.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/traceability/TraceabilityDetailResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/entity/dto/traceability/TraceabilityListResponse.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/exception/AuthExceptionHandler.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/exception/RateLimitException.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/exception/UnauthorizedException.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/interceptor/RateLimitInterceptor.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/AchievementBadgeRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/AchievementPointHistoryRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/AchievementRuleRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ActivityCertificateRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ActivityHarvestRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ActivityItemRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/AdoptionTemplateRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/BannerRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/CameraRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/CartOrderItemRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/CartOrderRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/CategoryGoodsRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/CategoryRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/CertificationRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/CityRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/CommentRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ConfigRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ContentRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/CustomerServiceFaqRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/CustomerServiceInfoRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/DemoSiteRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/DistrictRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/FeedbackReplyRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/FeedbackTypeRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/FiveGoodRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/LogisticsTrackingRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/MemberLevelRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/MonitorDeviceRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/OrderAddressRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/OrderInfoRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/OrderItemRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/OrderLogisticsRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/PointsRecordRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ProductRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ProductTagRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ProductionRecordRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/PromotionRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/PromotionTypeRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ProvinceRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ResearchActivityImageRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ResearchActivityNoticeRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ResearchActivityRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ResearchActivityRewardRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ResearchActivityScheduleRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ReservationQueryRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ReservationRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/ShoppingCartRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/SkuRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/SpecRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/SpuRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/TasteActivityRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/TasteApplicationRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/TraceabilityInfoRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/TraceabilityShareRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/UserAchievementRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/UserActivityItemRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/UserAddressRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/UserBadgeProgressRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/UserBehaviorStatsRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/UserFeedbackRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/UserFollowRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/UserLikeRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/UserPointsRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/UserRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/WyAdoptionOrderRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/WyAdoptionPackageRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/WyAdoptionProjectRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/WyDemoSiteRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/WyFaqRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/WyGiftRecordRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/WyGiftRuleRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/WyGrowthLogRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/WyHarvestPeriodRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/WyMemberLevelRulesRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/WyMemberPointsLogRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/WyPackageBenefitRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/WyResourceRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/WyUserRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/repository/WyUserReviewRepository.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/AchievementService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/ActivityHarvestService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/AdoptionGiftService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/AdoptionProjectService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/AdoptionService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/AuthService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/BackendApiService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/BannerService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/CartOrderService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/CategoryService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/CommentService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/ConfigService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/ContentService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/CustomerServiceService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/DemoSiteService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/FeedbackService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/FiveGoodService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/MonitorDeviceService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/OrderService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/PaymentApiService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/PointsService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/ProductService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/PromotionService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/PromotionTypeService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/RegionService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/ResearchActivityService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/ReservationQueryService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/ReservationService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/ShoppingCartService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/TasteActivityService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/TraceabilityService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/UserAddressService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/UserFollowService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/UserLikeService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/UserProfileService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/UserService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/WyAdoptionOrderService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/WyAdoptionPackageService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/WyDemoSiteService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/WyFaqService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/WyResourceService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/WyUserReviewService.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/AchievementServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/ActivityHarvestServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/AdoptionGiftServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/AdoptionProjectServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/AdoptionServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/AuthServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/BackendApiServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/BannerServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/CartOrderServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/CategoryServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/CommentServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/ConfigServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/ContentServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/CustomerServiceServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/FeedbackServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/MonitorDeviceServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/OrderServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/PaymentApiServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/PointsServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/ProductServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/PromotionServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/PromotionTypeServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/RegionServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/ResearchActivityServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/ReservationQueryServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/ReservationServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/ShoppingCartServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/TasteActivityServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/TraceabilityServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/UserAddressServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/UserFollowServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/UserLikeServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/UserProfileServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/UserServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/WyAdoptionOrderServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/WyAdoptionPackageServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/WyDemoSiteServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/WyFaqServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/WyResourceServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/service/impl/WyUserReviewServiceImpl.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/task/GiftExpireTask.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/util/OrderNoUtils.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/utils/HtmlUtils.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/com/miniot/fengdu/utils/PriceConverterUtils.class" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/classes/doc.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/maven-archiver/pom.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/createdFiles.lst" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/inputFiles.lst" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/surefire-reports/TEST-com.miniot.fengdu.controller.CartOrderControllerTest.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/surefire-reports/TEST-com.miniot.fengdu.integration.ShoppingCartCompleteFlowTest.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/surefire-reports/TEST-com.miniot.fengdu.service.CartCheckoutIntegrationTest.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/surefire-reports/TEST-com.miniot.fengdu.service.CartOrderServiceTest.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/surefire-reports/TEST-com.miniot.fengdu.service.ShoppingCartServiceTest.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/surefire-reports/com.miniot.fengdu.controller.CartOrderControllerTest.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/surefire-reports/com.miniot.fengdu.integration.ShoppingCartCompleteFlowTest.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/surefire-reports/com.miniot.fengdu.service.CartCheckoutIntegrationTest.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/surefire-reports/com.miniot.fengdu.service.CartOrderServiceTest.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/surefire-reports/com.miniot.fengdu.service.ShoppingCartServiceTest.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/wuyou-project-1.0-SNAPSHOT.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/target/wuyou-project-1.0-SNAPSHOT.jar.original" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/temp/接口文档模版.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-project小程序/test.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-server/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-server/.idea/compiler.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-server/.idea/encodings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-server/.idea/jarRepositories.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wuyou-server/.idea/misc.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/后台地址.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/购物车接口文档.md" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\apache-maven-3.8.6-bin\maven-repository" />
        <option name="userSettingsFile" value="D:\apache-maven-3.8.6-bin\apache-maven-3.8.6\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="307P3x3HbT7Q8QQzxJl6FG0Z08Y" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.payment-demo [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.payment-demo [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.wuyou-back-project [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.wuyou-project [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.wuyou-project [test].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.FengDuStarApplication (1).executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.FengDuStarApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.PaymentDemoApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/CQGW2025/潼南五有/wuyou-server&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.miniot.fengdu.service.impl" />
      <recent name="com.miniot.fengdu.service" />
      <recent name="com.miniot.fengdu.controller" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.FengDuStarApplication (1)">
    <configuration name="FengDuStarApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="wuyou-back-project" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.miniot.fengdu.FengDuStarApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="FengDuStarApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="wuyou-project" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.miniot.fengdu.FengDuStarApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PaymentDemoApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="payment-demo" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.starlight.paymentdemo.PaymentDemoApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.24807.4" />
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.24807.4" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="44b71d58-dae5-43e1-a3a3-f2ea3b3b11ba" name="Changes" comment="" />
      <created>1752975443434</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752975443434</updated>
      <workItem from="1752975444575" duration="2892000" />
      <workItem from="1753061194701" duration="15579000" />
      <workItem from="1753100952556" duration="2899000" />
      <workItem from="1753147348870" duration="516000" />
      <workItem from="1753147885914" duration="31702000" />
      <workItem from="1753233651980" duration="14261000" />
      <workItem from="1753320299102" duration="25039000" />
      <workItem from="1753405911558" duration="18372000" />
      <workItem from="1753604699466" duration="5369000" />
      <workItem from="1753670224350" duration="111000" />
      <workItem from="1753670348716" duration="10627000" />
      <workItem from="1753754583778" duration="18453000" />
      <workItem from="1753838712099" duration="17291000" />
      <workItem from="1753877272377" duration="256000" />
      <workItem from="1753925258216" duration="2068000" />
      <workItem from="1753932278841" duration="6563000" />
      <workItem from="1753955854482" duration="815000" />
      <workItem from="1754011822360" duration="7765000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/payment-demo/src/main/java/com/starlight/paymentdemo/service/impl/WxPayServiceImpl.java</url>
          <line>367</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>