package com.miniot.fengdu.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miniot.fengdu.entity.CartOrder;
import com.miniot.fengdu.entity.OrderInfo;
import com.miniot.fengdu.entity.User;
import com.miniot.fengdu.entity.dto.OrderListDTO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 测试订单数据访问层 - 使用测试表
 */
@Mapper
public interface TestOrderRepository extends BaseMapper<OrderInfo> {

    /**
     * 分页查询测试单一商品订单列表
     */
    @Select({
        "<script>",
        "SELECT",
        "  o.order_no AS orderNo,",
        "  u.nickname AS userName,",
        "  u.phone AS userPhone,",
        "  CASE o.type",
        "    WHEN 1 THEN '五有'",
        "    WHEN 2 THEN '有礼'",
        "    WHEN 3 THEN '新研'",
        "    ELSE '其他'",
        "  END AS orderType,",
        "  CONCAT('¥', FORMAT(o.total_fee / 100, 2)) AS amount,",
        "  DATE_FORMAT(o.create_time, '%Y-%m-%d %H:%i') AS createTime,",
        "  o.order_status AS status,",
        "  1 AS productCount",
        "FROM t_order_info_test o",
        "LEFT JOIN wy_user_test u ON o.user_id = u.id",
        "WHERE 1=1",
        "<if test='status != null and status != \"\"'>",
        "  AND o.order_status = #{status}",
        "</if>",
        "<if test='type != null and type != \"\"'>",
        "  AND CASE o.type",
        "    WHEN 1 THEN '五有'",
        "    WHEN 2 THEN '有礼'",
        "    WHEN 3 THEN '新研'",
        "    ELSE '其他'",
        "  END = #{type}",
        "</if>",
        "<if test='startTime != null and startTime != \"\"'>",
        "  AND DATE(o.create_time) &gt;= #{startTime}",
        "</if>",
        "<if test='endTime != null and endTime != \"\"'>",
        "  AND DATE(o.create_time) &lt;= #{endTime}",
        "</if>",
        "ORDER BY o.create_time DESC",
        "</script>"
    })
    IPage<OrderListDTO> selectTestOrderListPage(Page<OrderListDTO> page, 
                                               @Param("status") String status,
                                               @Param("type") String type,
                                               @Param("startTime") String startTime,
                                               @Param("endTime") String endTime);

    /**
     * 分页查询测试购物车订单列表
     */
    @Select({
        "<script>",
        "SELECT",
        "  co.order_no AS orderNo,",
        "  u.nickname AS userName,",
        "  u.phone AS userPhone,",
        "  '有礼' AS orderType,",
        "  CONCAT('¥', FORMAT(co.total_fee / 100, 2)) AS amount,",
        "  DATE_FORMAT(co.create_time, '%Y-%m-%d %H:%i') AS createTime,",
        "  co.order_status AS status,",
        "  co.item_count AS productCount",
        "FROM t_cart_order_test co",
        "LEFT JOIN wy_user_test u ON co.user_id = u.id",
        "WHERE 1=1",
        "<if test='status != null and status != \"\"'>",
        "  AND co.order_status = #{status}",
        "</if>",
        "<if test='type != null and type != \"\"'>",
        "  AND ('有礼' = #{type} OR '购物车订单' = #{type})",
        "</if>",
        "<if test='startTime != null and startTime != \"\"'>",
        "  AND DATE(co.create_time) &gt;= #{startTime}",
        "</if>",
        "<if test='endTime != null and endTime != \"\"'>",
        "  AND DATE(co.create_time) &lt;= #{endTime}",
        "</if>",
        "ORDER BY co.create_time DESC",
        "</script>"
    })
    IPage<OrderListDTO> selectTestCartOrderListPage(Page<OrderListDTO> page, 
                                                   @Param("status") String status,
                                                   @Param("type") String type,
                                                   @Param("startTime") String startTime,
                                                   @Param("endTime") String endTime);

    /**
     * 批量插入测试单一商品订单
     */
    @Insert({
        "<script>",
        "INSERT INTO t_order_info_test (",
        "  title, order_no, user_id, product_id, total_fee, order_status, type, order_parm, create_time, update_time",
        ") VALUES ",
        "<foreach collection='orders' item='order' separator=','>",
        "  (",
        "    #{order.title},",
        "    #{order.orderNo},",
        "    #{order.userId},",
        "    #{order.productId},",
        "    #{order.totalFee},",
        "    #{order.orderStatus},",
        "    #{order.type},",
        "    #{order.orderParm},",
        "    NOW(),",
        "    NOW()",
        "  )",
        "</foreach>",
        "</script>"
    })
    int batchInsertTestOrders(@Param("orders") java.util.List<OrderInfo> orders);

    /**
     * 批量插入测试购物车订单
     */
    @Insert({
        "<script>",
        "INSERT INTO t_cart_order_test (",
        "  title, order_no, user_id, total_fee, order_status, type, item_count, total_quantity, create_time, update_time",
        ") VALUES ",
        "<foreach collection='orders' item='order' separator=','>",
        "  (",
        "    #{order.title},",
        "    #{order.orderNo},",
        "    #{order.userId},",
        "    #{order.totalFee},",
        "    #{order.orderStatus},",
        "    #{order.type},",
        "    #{order.itemCount},",
        "    #{order.totalQuantity},",
        "    NOW(),",
        "    NOW()",
        "  )",
        "</foreach>",
        "</script>"
    })
    int batchInsertTestCartOrders(@Param("orders") java.util.List<CartOrder> orders);

    /**
     * 批量插入测试用户
     */
    @Insert({
        "<script>",
        "INSERT INTO wy_user_test (",
        "  username, nickname, phone, avatar, created_at, updated_at",
        ") VALUES ",
        "<foreach collection='users' item='user' separator=','>",
        "  (",
        "    #{user.username},",
        "    #{user.nickname},",
        "    #{user.phone},",
        "    #{user.avatar},",
        "    NOW(),",
        "    NOW()",
        "  )",
        "</foreach>",
        "</script>"
    })
    int batchInsertTestUsers(@Param("users") java.util.List<User> users);

    /**
     * 清空测试单一商品订单表
     */
    @Delete("DELETE FROM t_order_info_test")
    int clearTestOrders();

    /**
     * 清空测试购物车订单表
     */
    @Delete("DELETE FROM t_cart_order_test")
    int clearTestCartOrders();

    /**
     * 清空测试用户表
     */
    @Delete("DELETE FROM wy_user_test")
    int clearTestUsers();

    /**
     * 查询现有测试用户的username列表
     */
    @Select("SELECT username FROM wy_user_test ORDER BY id")
    java.util.List<String> selectExistingTestUsernames();

    /**
     * 查询现有测试用户的ID列表
     */
    @Select("SELECT id FROM wy_user_test ORDER BY id")
    java.util.List<Long> selectExistingTestUserIds();
}
