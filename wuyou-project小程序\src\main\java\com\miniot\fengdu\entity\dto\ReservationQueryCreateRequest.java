package com.miniot.fengdu.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "预约查询创建请求", description = "创建预约查询记录的请求参数")
public class ReservationQueryCreateRequest {
    
    @NotNull(message = "预约ID不能为空")
    @ApiModelProperty(value = "预约ID", required = true, example = "1001")
    private Long reservationId;

//    @NotNull(message = "活动名称")
    @ApiModelProperty(value = "预约ID", required = true, example = "1001")
    private String activityTitle;

    @ApiModelProperty(value = "预约ID", required = true, example = "1001")
    private String title;
    
    @NotNull(message = "用户ID不能为空")
    @ApiModelProperty(value = "用户ID", required = true, example = "2001")
    private Long userId;


    @TableField("activityLocation")
    private String activityLocation;

    @TableField("bookingTime")
    private String bookingTime;

    @TableField("contactName")
    private String contactName;

    @TableField("contactPhone")
    private String contactPhone;

    @TableField("participantCount")
    private Integer participantCount;

    @TableField("periodEndTime")
    private String periodEndTime;

    @TableField("periodStartTime")
    private String periodStartTime;

    @TableField("selectedDate")
    private String selectedDate;

    @TableField("selectedPeriod")
    private String selectedPeriod;

    @ApiModelProperty(value = "规格ID", required = true, example = "47")
    private Long specId;

    @ApiModelProperty(value = "规格单价", required = true, example = "68.00")
    private java.math.BigDecimal unitPrice;

    @ApiModelProperty(value = "用户openId（用于支付）", example = "oWmnN5PsRD-FVYbLYxQznP_j7fPU")
    private String openId;
}