package com.miniot.fengdu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 订单信息实体类
 */
@Data
@TableName("t_order_info")
@ApiModel(value = "订单信息实体", description = "订单信息")
public class OrderInfo {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "订单ID", example = "1")
    private Long id;

    @TableField("title")
    @ApiModelProperty(value = "订单标题", example = "有机蔬菜田地认养")
    private String title;

    @TableField("order_no")
    @ApiModelProperty(value = "商户订单编号", example = "ORD-20230615-001")
    private String orderNo;

    @TableField("user_id")
    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    @TableField("product_id")
    @ApiModelProperty(value = "支付产品ID", example = "1")
    private Long productId;

    @TableField("total_fee")
    @ApiModelProperty(value = "订单金额(分)", example = "250000")
    private Integer totalFee;

    @TableField("code_url")
    @ApiModelProperty(value = "订单二维码连接", example = "http://example.com/qr")
    private String codeUrl;

    @TableField("order_status")
    @ApiModelProperty(value = "订单状态", example = "处理中")
    private String orderStatus;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @TableField("open_id")
    @ApiModelProperty(value = "openid", example = "wx123456")
    private String openId;

    @TableField("type")
    @ApiModelProperty(value = "订单类型：1-认养，2-有礼，3-新研", example = "1")
    private Integer type;

    @TableField("order_parm")
    @ApiModelProperty(value = "订单规格", example = "100平米/年")
    private String orderParm;

    @TableField("remark")
    @ApiModelProperty(value = "备注", example = "100平米/年")
    private String remark;

    @TableField("express_no")
    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    @TableField("express_company")
    @ApiModelProperty(value = "快递公司")
    private String expressCompany;

    @TableField("task_flag")
    @ApiModelProperty(value = "积分定时任务标志 0:未统计 1:已统计")
    private Integer taskFlag;

    @TableField("username")
    @ApiModelProperty(value = "用户名", example = "张三")
    private String username;

    @TableField("phone")
    @ApiModelProperty(value = "手机号", example = "13800138000")
    private String phone;

}