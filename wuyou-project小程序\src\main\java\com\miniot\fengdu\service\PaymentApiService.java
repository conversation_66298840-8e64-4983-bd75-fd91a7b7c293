package com.miniot.fengdu.service;

import com.miniot.fengdu.entity.CartOrder;

import java.util.Map;

/**
 * 支付服务API调用接口
 */
public interface PaymentApiService {
    
    /**
     * 调用支付服务创建小程序支付订单
     * @param cartOrder 购物车订单
     * @param openId 用户openId
     * @return 支付参数
     */
    Map<String, Object> createJsapiPayment(CartOrder cartOrder, String openId);
    
    /**
     * 查询支付订单状态
     * @param orderNo 订单号
     * @return 支付状态信息
     */
    Map<String, Object> queryPaymentStatus(String orderNo);
    
    /**
     * 取消支付订单
     * @param orderNo 订单号
     * @return 取消结果
     */
    boolean cancelPaymentOrder(String orderNo);

    /**
     * 申请购物车订单退款
     * @param orderNo 订单号
     * @param refundNo 退款单号
     * @param refundFee 退款金额(分)
     * @param reason 退款原因
     * @return 是否成功
     */
    boolean applyCartOrderRefund(String orderNo, String refundNo, Integer refundFee, String reason);

    /**
     * 调用支付服务创建规格商品支付订单
     * @param specId 规格ID
     * @param openId 用户openId
     * @param remark 备注信息
     * @return 支付参数
     */
    Map<String, Object> createSpecPayment(Long specId, String openId, String remark);

    /**
     * 查询购物车订单退款状态
     * @param refundNo 退款单号
     * @return 退款状态信息
     */
    Map<String, Object> queryCartOrderRefund(String refundNo);
}
