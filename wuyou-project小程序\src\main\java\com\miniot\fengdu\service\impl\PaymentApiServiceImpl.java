package com.miniot.fengdu.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.miniot.fengdu.config.PaymentServiceConfig;
import com.miniot.fengdu.entity.CartOrder;
import com.miniot.fengdu.service.PaymentApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付服务API调用实现类
 */
@Slf4j
@Service
public class PaymentApiServiceImpl implements PaymentApiService {

    @Autowired
    private PaymentServiceConfig paymentConfig;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public Map<String, Object> createJsapiPayment(CartOrder cartOrder, String openId) {
        try {
            log.info("调用支付服务创建小程序支付订单，订单号：{}", cartOrder.getOrderNo());

            // 构建请求URL
            String url = paymentConfig.getBaseUrl() + paymentConfig.getJsapiPath() + "/" + paymentConfig.getDefaultProductId()
                    + "?openid=" + openId + "&type=4"; // type=4 表示购物车订单

            // 构建请求体 (PayParm)
            Map<String, Object> payParm = new HashMap<>();
            // 🔧 修复：在备注中传递订单金额，格式：AMOUNT:金额(分)
            String remarkWithAmount = "AMOUNT:" + cartOrder.getTotalFee();
            if (cartOrder.getRemark() != null && !cartOrder.getRemark().isEmpty()) {
                remarkWithAmount += "|" + cartOrder.getRemark();
            }
            payParm.put("remark", remarkWithAmount);
            payParm.put("parms", cartOrder.getOrderNo()); // 将订单号作为参数传递

            log.info("传递给支付服务的订单金额：{}分，订单号：{}", cartOrder.getTotalFee(), cartOrder.getOrderNo());

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(payParm, headers);

            log.info("支付服务请求URL：{}", url);
            log.info("支付服务请求参数：{}", objectMapper.writeValueAsString(payParm));

            // 调用支付服务
            ResponseEntity<Map> response = restTemplate.postForEntity(url, requestEntity, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                log.info("支付服务响应：{}", objectMapper.writeValueAsString(responseBody));

                // 检查响应是否成功 (支付服务返回 code=0 表示成功)
                Integer code = (Integer) responseBody.get("code");
                if (code != null && code == 0) {
                    Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
                    if (data != null) {
                        log.info("支付订单创建成功，订单号：{}", cartOrder.getOrderNo());
                        return data;
                    }
                }
                
                log.error("支付服务返回错误：{}", responseBody.get("message"));
                throw new RuntimeException("支付服务返回错误：" + responseBody.get("message"));
            } else {
                log.error("支付服务调用失败，状态码：{}", response.getStatusCode());
                throw new RuntimeException("支付服务调用失败");
            }

        } catch (Exception e) {
            log.error("调用支付服务异常，订单号：{}，错误：{}", cartOrder.getOrderNo(), e.getMessage(), e);
            throw new RuntimeException("调用支付服务失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> queryPaymentStatus(String orderNo) {
        try {
            log.info("查询支付订单状态，订单号：{}", orderNo);

            // 构建请求URL
            String url = paymentConfig.getBaseUrl() + paymentConfig.getQueryPath() + "/" + orderNo;

            log.info("查询支付状态URL：{}", url);

            // 调用支付服务查询接口
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                log.info("支付状态查询响应：{}", objectMapper.writeValueAsString(responseBody));

                // 检查响应是否成功 (支付服务返回 code=0 表示成功)
                Integer code = (Integer) responseBody.get("code");
                if (code != null && code == 0) {
                    Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
                    if (data != null) {
                        log.info("支付状态查询成功，订单号：{}", orderNo);
                        return data;
                    }
                }
                
                log.error("支付状态查询失败：{}", responseBody.get("message"));
                throw new RuntimeException("支付状态查询失败：" + responseBody.get("message"));
            } else {
                log.error("支付状态查询失败，状态码：{}", response.getStatusCode());
                throw new RuntimeException("支付状态查询失败");
            }

        } catch (Exception e) {
            log.error("查询支付状态异常，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            throw new RuntimeException("查询支付状态失败：" + e.getMessage());
        }
    }

    @Override
    public boolean cancelPaymentOrder(String orderNo) {
        try {
            log.info("取消支付订单，订单号：{}", orderNo);

            // 构建请求URL
            String url = paymentConfig.getBaseUrl() + paymentConfig.getClosePath() + "/" + orderNo;

            log.info("取消支付订单URL：{}", url);

            // 调用支付服务关闭订单接口
            ResponseEntity<Map> response = restTemplate.postForEntity(url, null, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                log.info("取消支付订单响应：{}", objectMapper.writeValueAsString(responseBody));

                // 检查响应是否成功 (支付服务返回 code=0 表示成功)
                Integer code = (Integer) responseBody.get("code");
                if (code != null && code == 0) {
                    log.info("支付订单取消成功，订单号：{}", orderNo);
                    return true;
                }
                
                log.error("支付订单取消失败：{}", responseBody.get("message"));
                return false;
            } else {
                log.error("支付订单取消失败，状态码：{}", response.getStatusCode());
                return false;
            }

        } catch (Exception e) {
            log.error("取消支付订单异常，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean applyCartOrderRefund(String orderNo, String refundNo, Integer refundFee, String reason) {
        log.info("调用支付服务申请购物车订单退款，订单号：{}，退款单号：{}", orderNo, refundNo);

        try {
            String url = paymentConfig.getBaseUrl() + "/wxapi/wx-pay-v2/refund/cart-order/" + orderNo;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求参数
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("refundNo", refundNo);
            requestBody.put("refundFee", refundFee);
            requestBody.put("reason", reason);

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            log.info("退款请求URL：{}", url);
            log.info("退款请求参数：{}", requestBody);

            ResponseEntity<Map> response = restTemplate.postForEntity(url, requestEntity, Map.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> responseBody = response.getBody();
                if (responseBody != null) {
                    // 支付服务返回 code=0 表示成功
                    Object codeObj = responseBody.get("code");
                    String code = String.valueOf(codeObj);

                    if ("0".equals(code)) {
                        log.info("购物车订单退款申请成功，订单号：{}，响应：{}", orderNo, responseBody);
                        return true;
                    } else {
                        log.error("购物车订单退款申请失败，订单号：{}，错误码：{}，响应：{}", orderNo, code, responseBody);
                        return false;
                    }
                } else {
                    log.error("购物车订单退款申请失败，订单号：{}，响应体为空", orderNo);
                    return false;
                }
            } else {
                log.error("购物车订单退款申请失败，订单号：{}，状态码：{}", orderNo, response.getStatusCode());
                return false;
            }

        } catch (Exception e) {
            log.error("调用支付服务申请购物车订单退款异常，订单号：{}，错误：{}", orderNo, e.getMessage());
            return false;
        }
    }

    @Override
    public Map<String, Object> queryCartOrderRefund(String refundNo) {
        log.info("调用支付服务查询购物车订单退款状态，退款单号：{}", refundNo);

        try {
            String url = paymentConfig.getBaseUrl() + "/wxapi/wx-pay-v2/refund/cart-order/query/" + refundNo;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> requestEntity = new HttpEntity<>(headers);

            log.info("退款查询URL：{}", url);

            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, Map.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> responseBody = response.getBody();
                log.info("购物车订单退款查询成功，退款单号：{}，响应：{}", refundNo, responseBody);
                return responseBody;
            } else {
                log.error("购物车订单退款查询失败，退款单号：{}，状态码：{}", refundNo, response.getStatusCode());
                return null;
            }

        } catch (Exception e) {
            log.error("调用支付服务查询购物车订单退款异常，退款单号：{}，错误：{}", refundNo, e.getMessage());
            return null;
        }
    }

    @Override
    public Map<String, Object> createSpecPayment(Long specId, String openId, String remark) {
        try {
            log.info("调用支付服务创建规格商品支付订单，规格ID：{}", specId);

            // 构建请求URL - 使用 type=3 表示研学活动（支付服务会智能识别规格ID）
            String url = paymentConfig.getBaseUrl() + "/api/wx-pay-v2/jsapi/" + specId + "?openid=" + openId + "&type=3";

            // 构建请求体 (PayParm)
            Map<String, Object> payParm = new HashMap<>();
            if (remark != null && !remark.isEmpty()) {
                payParm.put("remark", remark);
            }

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(payParm, headers);

            log.info("支付服务请求URL：{}", url);
            log.info("支付服务请求参数：{}", objectMapper.writeValueAsString(payParm));

            // 调用支付服务
            ResponseEntity<Map> response = restTemplate.postForEntity(url, requestEntity, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> result = response.getBody();
                log.info("调用支付服务成功，规格ID：{}", specId);
                log.info("支付服务响应数据：{}", result);
                return result;
            } else {
                log.error("调用支付服务失败，HTTP状态码：{}，响应体：{}", response.getStatusCode(), response.getBody());
                throw new RuntimeException("调用支付服务失败，HTTP状态码：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("调用支付服务创建规格商品支付订单失败，规格ID：{}，错误：{}", specId, e.getMessage(), e);
            throw new RuntimeException("调用支付服务失败：" + e.getMessage(), e);
        }
    }
}
