# 测试订单接口更新说明

## 📝 更新内容

### 1. 新增字段支持
在 `t_order_info_test` 表中新增了两个字段：
- `username` (varchar) - 用户名
- `phone` (varchar) - 手机号

### 2. 接口更新

#### 2.1 创建测试订单接口更新
**接口路径**: `POST /back/api/admin/test-orders/create`

**新增请求参数**:
```json
{
  "orderNo": "ORDER_20231201123456",
  "userId": 1001,
  "totalFee": 25000,
  "type": 1,
  "createTime": "2023-12-01T14:30:00",
  "title": "有机蔬菜田地认养",
  "productId": 1001,
  "orderParm": "100平米/年",
  "remark": "测试订单",
  "username": "张三",        // 新增字段
  "phone": "13800138000"     // 新增字段
}
```

#### 2.2 新增专门查询接口
**接口路径**: `GET /back/api/admin/test-orders/info-list`

**功能**: 专门查询 `t_order_info_test` 表数据，支持用户名和手机号查询

**请求参数**:
| 参数名 | 类型 | 是否必须 | 默认值 | 说明 |
|--------|------|----------|--------|------|
| page | Integer | 否 | 1 | 页码 |
| size | Integer | 否 | 10 | 每页条数 |
| status | String | 否 | - | 订单状态 |
| type | String | 否 | - | 订单类型 |
| startTime | String | 否 | - | 开始时间 |
| endTime | String | 否 | - | 结束时间 |
| userName | String | 否 | - | 用户名（模糊查询） |
| userPhone | String | 否 | - | 手机号（模糊查询） |

**响应示例**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "orderNo": "ORDER_20231201123456",
        "userId": 1001,
        "userName": "张三",
        "userPhone": "13800138000",
        "orderType": "五有",
        "amount": "¥250.00",
        "createTime": "2023-12-01 14:30",
        "status": "支付成功",
        "title": "有机蔬菜田地认养",
        "productId": 1001,
        "orderParm": "100平米/年",
        "remark": "测试订单"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 3. 查询逻辑优化

原有的查询接口 `/back/api/admin/test-orders/list` 也已更新：
- 优先使用 `t_order_info_test` 表中的 `username` 和 `phone` 字段
- 如果这些字段为空，则回退到关联 `wy_user_test` 表的数据
- 使用 `COALESCE` 函数确保数据完整性

### 4. 数据兼容性

- 新字段为可选字段，不传值时不会影响现有功能
- 查询时会自动处理空值，显示"未设置"
- 完全向后兼容现有的测试订单数据

## 🔧 使用示例

### 创建带用户信息的测试订单
```bash
curl -X POST "http://localhost:8080/back/api/admin/test-orders/create" \
  -H "Content-Type: application/json" \
  -d '{
    "orderNo": "TEST_ORDER_001",
    "userId": 1001,
    "totalFee": 25000,
    "type": 1,
    "username": "张三",
    "phone": "13800138000"
  }'
```

### 按用户名查询测试订单
```bash
curl "http://localhost:8080/back/api/admin/test-orders/info-list?userName=张三"
```

### 按手机号查询测试订单
```bash
curl "http://localhost:8080/back/api/admin/test-orders/info-list?userPhone=138"
```

## ⚠️ 注意事项

1. 新字段 `username` 和 `phone` 为可选字段
2. 专门查询接口 `/info-list` 只查询 `t_order_info_test` 表
3. 原有查询接口 `/list` 仍然合并查询单一商品订单和购物车订单
4. 用户名和手机号支持模糊查询
5. 数据库字段类型为 varchar，请确保前端传入字符串类型
