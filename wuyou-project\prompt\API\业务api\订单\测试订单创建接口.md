# 测试订单创建接口文档

## 接口概述
新增的测试订单创建接口，用于向 `t_order_info_test` 表插入单条测试订单数据。

## 接口信息
- **请求路径**: `POST /back/api/admin/test-orders/create`
- **请求方式**: POST
- **接口描述**: 创建单条测试订单数据，订单状态默认为"支付成功"

## 请求参数

### 请求体 (JSON)
| 参数名 | 类型 | 是否必须 | 示例 | 备注 |
|--------|------|----------|------|------|
| orderNo | String | 是 | "ORDER_20231201123456" | 订单号，必须唯一 |
| userId | Long | 是 | 1001 | 用户ID，必须在wy_user_test表中存在 |
| totalFee | Integer | 是 | 25000 | 订单金额(分)，必须大于0 |
| type | Integer | 是 | 1 | 订单类型：1-五有，2-有礼，3-新研 |
| createTime | String | 否 | "2023-12-01T14:30:00" | 下单时间，不传则使用当前时间 |
| title | String | 否 | "有机蔬菜田地认养" | 订单标题，不传则根据type自动生成 |
| productId | Long | 否 | 1001 | 商品ID，不传则默认为1 |
| orderParm | String | 否 | "100平米/年" | 订单规格，不传则默认为"默认规格" |
| remark | String | 否 | "测试订单" | 备注信息 |

### 请求示例
```json
{
  "orderNo": "ORDER_20231201123456",
  "userId": 1001,
  "totalFee": 25000,
  "type": 1,
  "createTime": "2023-12-01T14:30:00",
  "title": "有机蔬菜田地认养",
  "productId": 1001,
  "orderParm": "100平米/年",
  "remark": "测试订单"
}
```

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "msg": "success",
  "path": null,
  "extra": null,
  "timestamp": "1742977717992",
  "isError": false,
  "isSuccess": true,
  "data": "成功创建测试订单: ORDER_20231201123456"
}
```

### 失败响应
```json
{
  "code": 500,
  "msg": "fail",
  "path": null,
  "extra": null,
  "timestamp": "1742977717992",
  "isError": true,
  "isSuccess": false,
  "data": "创建测试订单失败：订单类型必须是1(五有)、2(有礼)或3(新研)"
}
```

## 业务规则

### 1. 订单状态
- 订单状态固定为"支付成功"，无需前端传参

### 2. 订单类型映射
- 1: 五有 (默认标题: "五有测试订单")
- 2: 有礼 (默认标题: "有礼测试订单")  
- 3: 新研 (默认标题: "新研测试订单")

### 3. 数据验证
- 订单号必须唯一
- 用户ID必须在 `wy_user_test` 表中存在
- 订单金额必须大于0
- 订单类型必须是1、2、3中的一个

### 4. 默认值处理
- `createTime`: 不传则使用当前时间
- `title`: 不传则根据订单类型自动生成
- `productId`: 不传则默认为1
- `orderParm`: 不传则默认为"默认规格"
- `updateTime`: 自动设置为当前时间

## 错误码说明

| 错误信息 | 说明 |
|----------|------|
| "订单类型必须是1(五有)、2(有礼)或3(新研)" | 订单类型参数不正确 |
| "用户ID不存在，请确保用户在wy_user_test表中存在" | 用户ID在测试用户表中不存在 |
| "订单号不能为空" | 订单号参数为空 |
| "用户ID不能为空" | 用户ID参数为空 |
| "订单金额不能为空" | 订单金额参数为空 |
| "订单金额必须大于0" | 订单金额小于等于0 |

## 使用场景
1. **单个订单测试**: 创建特定参数的测试订单
2. **数据补充**: 为测试环境补充特定的订单数据
3. **接口调试**: 验证订单相关功能
4. **演示数据**: 创建用于演示的订单数据

## 注意事项
1. 该接口仅操作测试表 `t_order_info_test`，不影响生产数据
2. 创建订单前请确保测试用户数据已存在
3. 订单号需要保证唯一性，建议使用时间戳等方式生成
4. 金额单位为分，需要注意换算关系
