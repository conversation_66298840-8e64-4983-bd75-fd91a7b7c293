package com.miniot.fengdu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miniot.fengdu.entity.ReservationQuery;
import com.miniot.fengdu.entity.dto.ReservationQueryCreateRequest;
import com.miniot.fengdu.entity.dto.ReservationQueryListResponse;
import com.miniot.fengdu.entity.dto.ReservationQueryResponse;
import com.miniot.fengdu.repository.ReservationQueryRepository;
import com.miniot.fengdu.service.ReservationQueryService;
import com.miniot.fengdu.service.ReservationValidationService;
import com.miniot.fengdu.service.PaymentApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
public class ReservationQueryServiceImpl extends ServiceImpl<ReservationQueryRepository, ReservationQuery> implements ReservationQueryService {

    @Autowired
    private ReservationValidationService reservationValidationService;

    @Autowired
    private PaymentApiService paymentApiService;

    @Override
    public ReservationQueryResponse getReservationQueryByCode(String queryCode) {
        return baseMapper.getReservationQueryByCode(queryCode);
    }
    
    @Override
    public Page<ReservationQueryListResponse> getUserReservationQueries(Long userId, int page, int size) {
        Page<ReservationQueryListResponse> pageParam = new Page<>(page, size);
        return baseMapper.getUserReservationQueries(pageParam, userId);
    }
    
    @Override
    @Transactional
    public boolean checkIn(String queryCode, Long userId) {
        // 获取预约查询记录
        ReservationQueryResponse query = getReservationQueryByCode(queryCode);
        
        // 验证查询码是否有效
        if (query == null || query.getIsValid() == 0) {
            return false;
        }
        
        // 验证是否已签到
        if (query.getCheckInStatus() == 1) {
            return false;
        }
        
        // 验证是否过期
        if (query.getExpireTime() != null && query.getExpireTime().isBefore(LocalDateTime.now())) {
            return false;
        }
        
        // 更新签到状态
        return baseMapper.updateCheckInStatus(queryCode) > 0;
    }
    
    @Override
    @Transactional
    public ReservationQuery createForReservation(ReservationQueryCreateRequest request) {
        try {
            // 验证规格信息
            if (!reservationValidationService.validateSpecInfo(request)) {
                log.error("规格信息验证失败: specId={}, unitPrice={}",
                         request.getSpecId(), request.getUnitPrice());
                return null;
            }

            // 检查是否已存在查询记录
           /* ReservationQuery existingQuery = getByReservationId2(request);
            if (existingQuery != null) {
                if(existingQuery.getExpireTime().equals(requestget.))
                return existingQuery;
            }*/

            // 生成查询码
            String queryCode = generateQueryCode();
            
            // 生成二维码URL (实际项目中应该调用二维码生成服务)
            String qrCodeUrl = "https://example.com/qrcode?qr=" + queryCode+"&userId="+request.getUserId();
//                    + ".png";
            
            // 创建查询记录
            ReservationQuery query = new ReservationQuery();
            query.setReservationId(request.getReservationId());
            if(StringUtils.isNotBlank(request.getActivityTitle())){
                query.setActivityTitle(request.getActivityTitle());
            }else{
                query.setActivityTitle(request.getTitle());
            }

            query.setUserId(request.getUserId());
            query.setQueryCode(queryCode);
            query.setQrCode(qrCodeUrl);
            query.setCheckInStatus(0);
            query.setIsValid(1);
            query.setExpireTime(LocalDateTime.now().plusDays(30)); // 设置30天后过期
            query.setActivityLocation(request.getActivityLocation());
            query.setBookingTime(request.getBookingTime());
            query.setContactName(request.getContactName());
            query.setContactPhone(request.getContactPhone());
            query.setParticipantCount(request.getParticipantCount());
            query.setPeriodEndTime(request.getPeriodEndTime());
            query.setPeriodStartTime(request.getPeriodStartTime());
            query.setSelectedDate(request.getSelectedDate());
            query.setSelectedPeriod(request.getSelectedPeriod());

            // 设置规格信息
            query.setSpecId(request.getSpecId());
            query.setUnitPrice(request.getUnitPrice());

            // 保存查询记录
            save(query);

            // 🔧 预约创建成功后，自动发起支付（如果有规格ID和openId）
            if (query.getSpecId() != null && request.getOpenId() != null) {
                try {
                    log.info("预约创建成功，自动发起支付: specId={}, openId={}", query.getSpecId(), request.getOpenId());
                    String remark = "预约支付-" + query.getActivityTitle() + "-" + query.getContactName();
                    Map<String, Object> paymentResult = paymentApiService.createSpecPayment(query.getSpecId(), request.getOpenId(), remark);
                    log.info("支付订单创建成功: {}", paymentResult);
                    // 可以将支付结果存储到query中，或者返回给前端
                } catch (Exception e) {
                    log.error("自动发起支付失败: specId={}, openId={}, error={}", query.getSpecId(), request.getOpenId(), e.getMessage());
                    // 支付失败不影响预约创建，只记录日志
                }
            }

            return query;
        } catch (Exception e) {
            log.error("创建预约查询记录失败: reservationId={}, userId={}",
                     request.getReservationId(), request.getUserId(), e);
            return null;
        }
    }

    @Override
    public ReservationQuery getByReservationId(Long reservationId) {
        return baseMapper.getByReservationId(reservationId,reservationId);
    }


//    @Override
    public ReservationQuery getByReservationId2(ReservationQueryCreateRequest reservationId) {
        return baseMapper.getByReservationId(reservationId.getReservationId(),reservationId.getUserId());
    }


    
    /**
     * 生成查询码
     * 格式：RQ + 年月日 + 6位随机数
     */
    private String generateQueryCode() {
        String datePrefix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String randomSuffix = String.format("%06d", (int)(Math.random() * 1000000));
        return "RQ" + datePrefix + randomSuffix;
    }
    
    @Override
    public List<ReservationQuery> getReservationQueries(Long userId, Integer checkInStatus, Integer isValid) {
        QueryWrapper<ReservationQuery> queryWrapper = new QueryWrapper<>();
        
        if (userId != null) {
            queryWrapper.eq("user_id", userId);
        }
        
        if (checkInStatus != null) {
            queryWrapper.eq("check_in_status", checkInStatus);
        }
        
        if (isValid != null) {
            queryWrapper.eq("is_valid", isValid);
        }
        
        queryWrapper.orderByDesc("created_at");
        
        return list(queryWrapper);
    }
    
    @Override
    @Transactional
    public boolean invalidateQuery(Long id) {
        ReservationQuery query = getById(id);
        if (query == null) {
            return false;
        }
        
        query.setIsValid(0);
        return updateById(query);
    }
    
    @Override
    @Transactional
    public int batchInvalidateQueries(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }
        
        UpdateWrapper<ReservationQuery> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", ids)
                    .set("is_valid", 0)
                    .set("updated_at", LocalDateTime.now());
        
        return baseMapper.update(null, updateWrapper);
    }
} 